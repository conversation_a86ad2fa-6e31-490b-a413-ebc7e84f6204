const AWS = require('aws-sdk');
const { createLogger } = require('./logger');

const logger = createLogger('chatHistory');

const docClient = new AWS.DynamoDB.DocumentClient();

/**
 * Fetches the last 5 chat messages for a user
 * @param {Object} ddb - The DynamoDB instance (kept for backward compatibility)
 * @param {string} userId - The ID of the user
 * @param {string} [chatType] - Optional chat type to filter by
 * @returns {Promise<Array>} - Array of chat messages or empty array on error
 */
async function getChatHistory(ddb, userId, chatType = null) {
  const startTime = Date.now();
  const operation = 'getChatHistory';
  
  if (!userId) {
    logger.error('User ID is required', { operation });
    return [];
  }
  
  try {
    logger.info('🔍 [CHAT_HISTORY_DEBUG] getChatHistory called', { operation, userId, chatType });

    const tableName = `ChatGPT-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

    // Build filter expression based on whether chatType is provided
    let filterExpression = 'userId = :userId AND isDeleted = :isDeleted';
    const expressionAttributeValues = {
      ':userId': userId,
      ':isDeleted': 'false'
    };

    if (chatType) {
      filterExpression += ' AND chatType = :chatType';
      expressionAttributeValues[':chatType'] = chatType;
    }

    const params = {
      TableName: tableName,
      FilterExpression: filterExpression,
      ExpressionAttributeValues: expressionAttributeValues,
      Limit: 5
    };

    logger.info('🔍 [CHAT_HISTORY_DEBUG] DynamoDB scan parameters', {
      operation,
      userId,
      chatType,
      tableName,
      filterExpression: params.FilterExpression,
      expressionAttributeValues: params.ExpressionAttributeValues,
      limit: params.Limit,
      isChatTypeFiltered: !!chatType
    });

    const data = await docClient.scan(params).promise();
    
    logger.info('📋 [CHAT_HISTORY_DEBUG] DynamoDB scan result', {
      operation,
      userId,
      hasData: !!data,
      itemCount: data?.Items?.length || 0,
      scannedCount: data?.ScannedCount || 0,
      count: data?.Count || 0
    });

    if (!data || !data.Items) {
      logger.warn('❌ [CHAT_HISTORY_DEBUG] No data returned from DynamoDB query', { operation, userId });
      return [];
    }

    logger.info('📋 [CHAT_HISTORY_DEBUG] Raw DynamoDB items before sorting', {
      operation,
      userId,
      rawItems: data.Items.map(item => ({
        id: item.id,
        type: item.type,
        chatType: item.chatType,
        role: item.role,
        message: item.message?.substring(0, 100) + (item.message?.length > 100 ? '...' : ''),
        createdAt: item.createdAt,
        timestamp: item.timestamp,
        userId: item.userId,
        isDeleted: item.isDeleted
      }))
    });

    const items = data.Items
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 5);

    logger.info('✅ [CHAT_HISTORY_DEBUG] Final sorted and limited chat history items', {
      operation,
      userId,
      itemCount: items.length,
      items: items.map(item => ({
        id: item.id,
        type: item.type,
        chatType: item.chatType,
        role: item.role,
        message: item.message?.substring(0, 100) + (item.message?.length > 100 ? '...' : ''),
        createdAt: item.createdAt,
        timestamp: item.timestamp
      }))
    });

    return items;
  } catch (error) {
    const errorMessage = `Error in ${operation} for user ${userId}: ${error.message}`;
    logger.error(errorMessage, {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name,
        ...(error.code && { code: error.code })
      },
      operation,
      userId
    });
    
    return [];
  }
}

/**
 * Formats chat history into a readable string
 * @param {Array} chatHistory - Array of chat messages
 * @returns {string} - Formatted chat history string or empty string on error
 */
function formatChatHistory(chatHistory) {
  const operation = 'formatChatHistory';
  
  try {
    if (!chatHistory || !Array.isArray(chatHistory)) {
      logger.error('Invalid chat history format', { 
        operation, 
        expected: 'array', 
        actual: typeof chatHistory 
      });
      return '';
    }
    
    if (chatHistory.length === 0) {
      logger.debug('Empty chat history array provided', { operation });
      return '';
    }

    const validMessages = chatHistory.filter(msg => 
      msg && 
      typeof msg === 'object' && 
      msg.message && 
      typeof msg.message === 'string' &&
      msg.role
    );

    if (validMessages.length === 0) {
      logger.warn('No valid messages found in chat history', { operation });
      return '';
    }

    const sortedHistory = [...validMessages].sort((a, b) => {
      try {
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
        return dateA - dateB;
      } catch (error) {
        logger.error('Error sorting messages by date', { 
          operation, 
          error: error.message 
        });
        return 0;
      }
    });

    const formattedMessages = sortedHistory.map((msg, index) => {
      try {
        const role = msg.role === 'user' ? 'User' : 'Assistant';
        const message = (msg.message || '').trim();
        return `- ${role}: ${message}`;
      } catch (error) {
        logger.error('Error formatting message', { 
          operation, 
          index, 
          error: error.message 
        });
        return null;
      }
    }).filter(Boolean);

    if (formattedMessages.length === 0) {
      logger.warn('No messages could be formatted', { operation });
      return '';
    }

    return formattedMessages.join('\n');
    
  } catch (error) {
    logger.error('Unexpected error formatting chat history', { 
      operation, 
      error: error.message,
      stack: error.stack
    });
    return '';
  }
}

/**
 * Gets the chat history prompt to be prepended to the main prompt
 * @param {Object} ddb - The DynamoDB instance
 * @param {string} userId - The ID of the user
 * @param {string} [chatType] - Optional chat type to filter by
 * @returns {Promise<string>} - Formatted chat history prompt or empty string on error
 */
async function getChatHistoryPrompt(ddb, userId, chatType = null) {
  const startTime = Date.now();
  const operation = 'getChatHistoryPrompt';
  
  // Input validation
  if (!userId) {
    logger.error('Missing required parameter: userId', { operation });
    return '';
  }

  try {
    logger.debug('Starting chat history prompt generation', { operation, userId });
    
    const chatHistory = await getChatHistory(ddb, userId, chatType);
    
    // logger.debug('Retrieved chat history', { 
    //   operation, 
    //   hasChatHistory: !!chatHistory,
    //   chatHistoryLength: chatHistory?.length || 0 
    // });
    
    if (!chatHistory || !Array.isArray(chatHistory) || chatHistory.length === 0) {
      logger.debug('No chat history found for user', { operation, userId });
      return '';
    }

    logger.debug('Formatting chat history', { 
      operation, 
      itemCount: chatHistory.length 
    });
    const formattedHistory = formatChatHistory(chatHistory);
    const result = `\n\nPrevious conversation:\n${formattedHistory}`;
    
    logger.debug('Successfully generated chat history prompt', { 
      operation, 
      userId,
      promptLength: result.length,
      preview: result.substring(0, 200) + (result.length > 200 ? '...' : '')
    });
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorDetails = {
      message: error.message,
      name: error.name,
      stack: error.stack,
      userId,
      operation,
      duration
    };
    
    logger.error('Error generating chat history prompt', { 
      operation, 
      durationMs: duration,
      error: errorDetails 
    });
    
    if (error.graphQLErrors) {
      logger.error('GraphQL errors', { 
        operation, 
        errors: error.graphQLErrors 
      });
    }
    if (error.networkError) {
      logger.error('Network error', { 
        operation, 
        message: error.networkError.message,
        statusCode: error.networkError.statusCode,
        body: error.networkError.body
      });
    }
    
    return '';
  }
}

module.exports = {
  getChatHistory,
  formatChatHistory,
  getChatHistoryPrompt,
};
