const { v4: uuidv4 } = require('uuid');
const { createLogger, logError, logSuccess } = require('../shared/utils/logger');
const { getItem, scanItems } = require('../shared/database/dynamoUtils');
const { 
  DEFAULT_STATIC_RESPONSES, 
  ChatTypeEnum,
  TABLES 
} = require('../shared/utils/constants');
const { getChatContext } = require('./chatHistoryService');

// Import prompt store functions
const {
  getChatbotPrompt,
  getChatbotPromptDataFromDynamoDB,
  _storeChatbotPromptInDynamoDB,
  removeChatbotPromptFromDynamoDB
} = require('./promptStore');

// Import GraphQL queries
const {
  GET_USER_FOR_COMMUNITY,
  GET_USER_FOR_FAMILY
} = require('../graphql/queries');

// Import utility functions
const {
  fetchBasicUserData,
  fetchKnowledgeData,
  fetchUnifiedData,
  extractOrganizations
} = require('../shared/utils/chatbotUtils');

// Import static prompt handlers
const {
  getCommunityEventsPrompt,
  getFamilyPrompt,
  getKnowledgePrompt,
  getUnifyPrompt,
} = require('../handlers/static');

// Import dynamic chatbot handlers
const dynamicChatbot = require('./dynamicChatbot');

// Import dynamic chat handlers
const {
  communityChatbot,
  familyChatbot,
  knowledgeChatbot,
  unifyChatbot
} = require('../handlers/dynamic');

const logger = createLogger('chatbot');

/**
 * Validates chatbot input parameters
 * @param {string} question - The user's question
 * @param {string} userId - The ID of the user
 * @param {string} chatType - The type of chat (Community, Family, Knowledge, Unify)
 * @param {Object} loggerWithContext - Logger instance with request context
 * @throws {Error} If any validation fails
 */
function validateChatbotInputs(question, userId, chatType, loggerWithContext) {
  // Log validation start
  loggerWithContext.debug('Validating chatbot inputs');

  // Validate required inputs
  if (!userId) {
    const error = new Error('Missing required parameter: userId is required');
    loggerWithContext.error('Validation error', { error: error.message });
    throw error;
  }
  
  if (!chatType) {
    const error = new Error('Missing required parameter: chatType is required');
    loggerWithContext.error('Validation error', { error: error.message });
    throw error;
  }
  
  if (!question || typeof question !== 'string' || !question.trim()) {
    const error = new Error('Invalid or empty question provided');
    loggerWithContext.error('Validation error', { error: error.message });
    throw error;
  }

  loggerWithContext.debug('Input validation successful');
}

async function getChatbotAnswer(question, userId, chatType, apolloClient) {
  // Initialize sequence counter and log context
  let logSequence = 1;
  const operation = 'getChatbotAnswer';
  const requestId = `chat-${uuidv4()}`;
  const startTime = Date.now();
  const logStep = (message, data = {}) => {
    loggerWithContext.info({ sequence: logSequence++, ...data }, message);
  };
  const logErrorStep = (message, error, data = {}) => {
    loggerWithContext.error({ 
      sequence: `✗${logSequence++}`, 
      error: error?.message || 'Unknown error',
      stack: error?.stack,
      ...data 
    }, message);
  };

  // Create logger with request context
  const loggerWithContext = logger.child({
    operation,
    requestId,
    chatType,
    userId,
    timestamp: new Date().toISOString(),
  });

  // Log execution start
  // logStep('1. 🚀 Starting BedrockChatBot execution', {
  //   question: question?.substring(0, 100) + (question?.length > 100 ? '...' : ''),
  //   chatType,
  //   hasApolloClient: !!apolloClient,
  //   executionStart: new Date().toISOString(),
  // });

  // Track if we should use static fallback
  let useStaticFallback = false;
  let staticFallbackResponse = null;
  
  try {
    // Main try block for the entire function
    // Warn if Apollo Client is missing
    if (!apolloClient) {
      logStep('2. ⚠️ Apollo Client is not available. Chat history will not be included.');
    } else {
      logStep('2. ✅ Apollo Client is available');
    }

    // Validate inputs
    logStep('3. 🔍 Validating input parameters');
    validateChatbotInputs(question, userId, chatType, loggerWithContext);
    logStep('4. ✅ Input validation successful');

    // Fetch prompt configuration from DynamoDB
    let promptConfig;
    try {
      logStep('5. 📝 Fetching prompt configuration from DynamoDB', { 
        chatType,
        tableName: 'chatbotPrompt'
      });
      
      // Try to get the dynamic prompt
      promptConfig = await getChatbotPrompt(chatType);
      
      // If no dynamic config, prepare static fallback but continue to try Bedrock
      if (!promptConfig || !promptConfig.template) {
        logStep('6. ⚠️ No dynamic prompt configuration found, will try Bedrock with default settings', {
          hasPromptConfig: !!promptConfig,
          hasTemplate: !!promptConfig?.template,
          table: TABLES.CHATBOT_PROMPT,
        });
        
        // Generate static response as fallback in case Bedrock fails
        logStep('7. ⏳ Preparing static response as fallback');
        staticFallbackResponse = await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
        useStaticFallback = true;
        
        logStep('8. ✅ Static fallback response prepared', {
          responseLength: staticFallbackResponse?.length || 0,
          responsePreview: staticFallbackResponse?.substring(0, 100) + (staticFallbackResponse?.length > 100 ? '...' : '')
        });
      } else {
        logStep('6. ✅ Successfully fetched prompt configuration', {
          configKeys: Object.keys(promptConfig),
          hasTemplate: !!promptConfig.template,
          hasInstructions: !!promptConfig.instructions,
        });
      }
    } catch (error) {
      logErrorStep('✗ Error fetching prompt configuration', error, {
        chatType,
        requestId,
      });
      
      logStep('✗ Falling back to static response due to configuration error');
      const staticResponse = await getStaticChatbotAnswer(question, userId, chatType, apolloClient);

      const endTime = Date.now();
      logStep(`✗ Execution completed with error fallback in ${endTime - startTime}ms`, {
        responseLength: staticResponse?.length || 0,
        executionTimeMs: endTime - startTime,
        executionEnd: new Date().toISOString(),
        responseType: 'error_fallback',
      });

      return staticResponse;
    }

    // Get user-specific data for the prompt
    let promptData = {};
    if (apolloClient) {
      try {
        logStep('9. 🔍 Fetching user-specific prompt data from DynamoDB', { 
          tableName: process.env.CHATBOT_PROMPTS_DATA_TABLE || 'ChatbotPromptsData'
        });
        
        promptData = await getChatbotPromptDataFromDynamoDB(
          userId,
          chatType,
          apolloClient
        );
        
        if (!promptData || Object.keys(promptData).length === 0) {
          logStep('10. ⚠️ No user-specific prompt data found in DynamoDB', { 
            tableName: process.env.CHATBOT_PROMPTS_DATA_TABLE || 'ChatbotPromptsData'
          });
        } else {
          logStep('10. ✅ Successfully retrieved user-specific prompt data', { 
            dataKeys: Object.keys(promptData),
            hasOrganizations: 'organizations' in promptData,
            organizationCount: promptData.organizations?.length || 0,
            dataPreview: JSON.stringify(promptData).substring(0, 200) + 
              (JSON.stringify(promptData).length > 200 ? '...' : '')
          });
        }
      } catch (error) {
        logErrorStep('✗ Error fetching user prompt data', error, {
          tableName: process.env.CHATBOT_PROMPTS_DATA_TABLE || 'ChatbotPromptsData'
        });
        logStep('✗ Falling back to static response due to error fetching user data');
        return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
      }
    }

    // If we get here but have no prompt data, use static fallback if available
    if (!promptData || Object.keys(promptData).length === 0) {
      logStep('11. ⚠️ No prompt data available, checking for static fallback');
      if (useStaticFallback && staticFallbackResponse) {
        logStep('12. 🔄 Using pre-generated static fallback response');
        return staticFallbackResponse;
      }
      // If we don't have a static fallback, generate one now
      logStep('12. ⏳ Generating new static fallback response');
      return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
    }

    const isDynamic = promptData.isDynamic || false;
    const promptDataString = JSON.stringify(promptData);
    logStep(`11. ✅ Found ${isDynamic ? 'dynamic' : 'static'} prompt data`, {
      promptLength: promptDataString.length,
      promptStart: promptDataString.substring(0, 100) + '...',
      isDynamic: isDynamic,
      dataKeys: Object.keys(promptData)
    });

    // Map of dynamic handlers
    const chatbotTypeHandlers = {
      [ChatTypeEnum.COMMUNITY]: communityChatbot,
      [ChatTypeEnum.FAMILY]: familyChatbot,
      [ChatTypeEnum.KNOWLEDGE]: knowledgeChatbot,
      [ChatTypeEnum.UNIFY]: unifyChatbot,
    };

    logStep('12. 🔄 Available dynamic handlers for chat types', {
      availableHandlers: Object.keys(chatbotTypeHandlers),
    });

    const handler = chatbotTypeHandlers[chatType];
    if (!handler) {
      logStep('13. ⚠️ No dynamic handler found, falling back to static prompt', {
        chatType
      });
      return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
    }

    logStep(`13. 🚀 Invoking dynamic handler: ${handler.name}`, {
      questionLength: question.length,
      promptDataLength: JSON.stringify(promptData).length,
      promptDataKeys: Object.keys(promptData)
    });

    try {
      const result = await handler(question, promptData, userId, apolloClient);
      logStep('14. ✅ Dynamic handler completed successfully', {
        resultLength: result?.length || 0,
        resultPreview: result?.substring(0, 100) + (result?.length > 100 ? '...' : '')
      });
      
      if (typeof result === 'string' && result.trim()) {
        return result;
      }
      throw new Error('Handler returned empty or invalid response');
    } catch (error) {
      logErrorStep('✗ Error in dynamic handler', error, {
        handlerName: handler?.name || 'unknown',
      });
      
      // Use pre-generated static fallback if available
      if (useStaticFallback && staticFallbackResponse) {
        logStep('✗ Using pre-generated static fallback after dynamic handler error');
        return staticFallbackResponse;
      }
      
      // Otherwise generate a new static response
      logStep('✗ Generating new static fallback after dynamic handler error');
      return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
    }
  } catch (unhandledError) {
    // Top‐level catch for any other errors
    logErrorStep('✗ Unexpected error in getChatbotAnswer', unhandledError);
    
    // Use pre-generated static fallback if available
    if (useStaticFallback && staticFallbackResponse) {
      logStep('✗ Using pre-generated static fallback after unhandled error');
      return staticFallbackResponse;
    }
    
    // Fall back to generating a new static answer on any unhandled exception
    logStep('✗ Generating new static fallback after unhandled error');
    return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
  } finally {
    const endTime = Date.now();
    logStep(`🏁 Execution completed in ${endTime - startTime}ms`, {
      executionTimeMs: endTime - startTime,
      executionEnd: new Date().toISOString(),
      totalSteps: logSequence - 1
    });
  }
}

// Chatbot handler functions have been moved to separate files in the handlers/dynamic directory

async function getChatbotPromptDataFromGraphQLApi(userId, apolloClient, type) {
  logger.debug('getChatbotPromptDataFromGraphQLApi called', { type });

  try {
    switch (type) {
      case ChatTypeEnum.COMMUNITY:
        return await fetchBasicUserData(apolloClient, GET_USER_FOR_COMMUNITY, userId);

      case ChatTypeEnum.FAMILY:
        return await fetchBasicUserData(apolloClient, GET_USER_FOR_FAMILY, userId);

      case ChatTypeEnum.KNOWLEDGE:
        return await fetchKnowledgeData(apolloClient, userId);

      case ChatTypeEnum.UNIFY: {
        // Return the object directly, not a string
        const result = await fetchUnifiedData(apolloClient, userId);
        return result;
      }

      default:
        logger.warn('Unknown chat type', { type });
        return null;
    }
  } catch (error) {
    logError(
      createLogger({}, { functionName: 'getChatbotPromptDataFromGraphQLApi' }),
      error,
      'Error in getChatbotPromptDataFromGraphQLApi'
    );
    return null;
  }
}

// ========== Helper Functions ==========


/**
 * Fallback function when no prompt configuration is found in DynamoDB
 * @param {string} question - The user's question
 * @param {string} userId - The ID of the user
 * @param {string} chatType - The type of chat (Community, Family, Knowledge, Unify)
 * @param {Object} apolloClient - The Apollo Client instance (kept for backward compatibility)
 * @returns {Promise<string>} - The chatbot's response
 */
async function getStaticChatbotAnswer(question, userId, chatType, apolloClient) {
  const requestId = `static-${uuidv4()}`;
  const operation = 'getStaticChatbotAnswer';
  const startTime = Date.now();
  
  // Create a child logger with request context
  const logger = createLogger(operation).child({
    requestId,
    userId,
    chatType,
    questionLength: question?.length || 0
  });
  
  console.log(`[${requestId}] [STATIC] Starting static chatbot answer generation`);
  logger.info('Starting static chatbot answer generation', {
    chatType,
    questionPreview: question ? question.substring(0, 50) + (question.length > 50 ? '...' : '') : 'No question'
  });
  logger.info({
    message: '🚀 Starting static chatbot answer generation',
    question: question?.substring(0, 100) + (question?.length > 100 ? '...' : ''),
    hasApolloClient: !!apolloClient,
    chatType,
  });
  
  try {
    console.log(`[${requestId}] [STATIC] Fetching user data from GraphQL API`);
    const userData = await getChatbotPromptDataFromGraphQLApi(userId, apolloClient, chatType);
    
    // logger.debug('Retrieved user data from GraphQL API', {
    //   hasUserData: !!userData,
    //   dataType: userData ? typeof userData : 'none',
    //   dataPreview: userData ? 'Data available' : 'No data'
    // });
    console.log(`[${requestId}] [STATIC] User data retrieved successfully`);
    
    // Get the appropriate handler based on chat type
    let handler;
    switch (chatType) {
      case ChatTypeEnum.COMMUNITY:
        console.log(`[${requestId}] [STATIC] Using Community handler`);
        handler = getCommunityEventsPrompt;
        break;
      case ChatTypeEnum.FAMILY:
        console.log(`[${requestId}] [STATIC] Using Family handler`);
        handler = getFamilyPrompt;
        break;
      case ChatTypeEnum.KNOWLEDGE:
        console.log(`[${requestId}] [STATIC] Using Knowledge handler`);
        handler = getKnowledgePrompt;
        break;
      case ChatTypeEnum.UNIFY:
        console.log(`[${requestId}] [STATIC] Using Unify handler`);
        handler = getUnifyPrompt;
        break;
      default:
        const warnMsg = `No handler found for chat type: ${chatType}`;
        console.warn(`[${requestId}] [STATIC] ${warnMsg}`);
        logger.warn(warnMsg);
    }
    
    logger.debug('Selected static prompt handler', {
      chatType,
      handler: handler ? handler.name : 'none'
    });
    
    let response = DEFAULT_STATIC_RESPONSES[chatType] || DEFAULT_STATIC_RESPONSES.default;
    let chatHistory = ''; // Initialize chatHistory at function scope
    
    console.log(`[${requestId}] [STATIC] Starting response generation`);
    
    try {
      // Get chat history for context
      console.log(`[${requestId}] [STATIC] Fetching chat history`);
      chatHistory = await getChatContext(userId, { limit: 3 });
      
      // Log the chat history details
      // const historyLog = {
      //   hasHistory: !!chatHistory,
      //   historyLength: chatHistory?.length || 0,
      //   preview: chatHistory ? chatHistory.substring(0, 50) + (chatHistory.length > 50 ? '...' : '') : ''
      // };
      
      // console.log(`[${requestId}] [STATIC] Chat history retrieved`, {
      //   hasHistory: historyLog.hasHistory,
      //   length: historyLog.historyLength
      // });
      
      // logger.debug('Retrieved chat history for static response', historyLog);
      
      // Log the raw userData before creating promptData
      // console.log(`[${requestId}] [STATIC] Raw userData:`, JSON.stringify(userData, null, 2));
      
      // Parse the userData if it's a string
      let parsedUserData;
      try {
        parsedUserData = typeof userData === 'string' ? JSON.parse(userData) : userData;
        console.log(`[${requestId}] [STATIC] Parsed userData type:`, typeof parsedUserData);
      } catch (parseError) {
        console.error(`[${requestId}] [STATIC] Error parsing userData:`, parseError);
        parsedUserData = null;
      }
      
      // Create promptData with parsed user data and chat history
      const promptData = parsedUserData && typeof parsedUserData === 'object' && !Array.isArray(parsedUserData)
        ? { 
            ...parsedUserData, 
            chatHistory: chatHistory || '',
            _metadata: {
              timestamp: new Date().toISOString(),
              chatType,
              userId
            }
          }
        : { 
            chatHistory: chatHistory || '',
            _metadata: {
              timestamp: new Date().toISOString(),
              chatType,
              userId,
              note: parsedUserData === null ? 'Error parsing userData' : 'No valid userData provided'
            }
          };
          
      // console.log(`[${requestId}] [STATIC] Created promptData with keys:`, Object.keys(promptData));
      // console.log(`[${requestId}] [STATIC] promptData content:`, JSON.stringify(promptData, null, 2));
          
      // Log the created promptData
      // console.log(`[${requestId}] [STATIC] Created promptData with keys:`, Object.keys(promptData));
      
      // Log the prompt data after appending chat history
      // logger.debug('Prompt data with chat history', {
      //   hasUserData: !!userData,
      //   userDataKeys: userData ? Object.keys(userData) : [],
      //   chatHistoryLength: chatHistory?.length || 0,
      //   chatHistoryPreview: chatHistory ? 
      //     chatHistory.substring(0, 100) + (chatHistory.length > 100 ? '...' : '') : 
      //     'No chat history',
      //   questionLength: question?.length || 0,
      //   questionPreview: question ? 
      //     question.substring(0, 100) + (question.length > 100 ? '...' : '') : 
      //     'No question'
      // });
      
      // Try to get a more specific response using the appropriate handler
      console.log(`[${requestId}] [STATIC] Generating prompt using handler`);
      let prompt;
      try {
        if (chatHistory) {
          // If we have chat history, prepend it to the question
          const questionWithHistory = `${chatHistory}Question: ${question}`;
          console.log(`[${requestId}] [STATIC] Using question with chat history`);
          prompt = await handler(questionWithHistory, promptData, userId, apolloClient);
        } else {
          // Otherwise, just use the original question
          console.log(`[${requestId}] [STATIC] Using question without chat history`);
          prompt = await handler(question, promptData, userId, apolloClient);
        }
        console.log(`[${requestId}] [STATIC] Prompt generated successfully`);
      } catch (handlerError) {
        console.error(`[${requestId}] [STATIC] Error in handler:`, handlerError.message);
        throw handlerError;
      }
      if (prompt && typeof prompt === 'string' && prompt.trim().length > 0) {
        response = prompt;
      } else if (userData) {
        // Extract organizations from the already parsed userData
        const orgs = extractOrganizations(userData);
        
        if (orgs.length > 0) {
          logger.debug('Extracted organizations', { 
            orgCount: orgs.length,
            orgNames: orgs.map(o => o.name).join(', ')
          });
          
          // If user asked about organizations and we have that data
          if (question.toLowerCase().includes('organization') || 
              question.toLowerCase().includes('org')) {
            response = `You are associated with ${orgs.length} organization(s): ` +
              orgs.map(o => o.name).join(', ');
          }
        }
      }
    } catch (error) {
      logger.error('Error processing user data for response', {
        error: error.message,
        stack: error.stack
      });
    }

    const executionTime = Date.now() - startTime;
    
    // Log the final response being returned
    console.log(`[${requestId}] [STATIC] Successfully generated response in ${executionTime}ms`, {
      responseLength: response?.length || 0,
      hasChatHistory: !!chatHistory,
      chatHistoryLength: chatHistory?.length || 0
    });
    
    logger.info('Successfully generated static response', {
      chatType,
      responseLength: response?.length || 0,
      executionTimeMs: executionTime,
      hasChatHistory: !!chatHistory,
      chatHistoryLength: chatHistory?.length || 0
    });

    // logger.debug('Final static response details', {
    //   chatType,
    //   fullResponse: response,
    //   promptData: JSON.stringify({
    //     question,
    //     userId,
    //     chatHistory: chatHistory || 'No chat history',
    //     userDataAvailable: !!userData
    //   }, null, 2)
    // });

    return response;
  } catch (error) {
    const errorTime = Date.now();
    const errorContext = {
      chatType,
      hasApolloClient: !!apolloClient,
      errorMessage: error.message,
      errorName: error.name,
      errorStack: error.stack,
      executionTimeMs: errorTime - startTime,
    };

    logError(logger, error, operation, errorContext);

    // Log the full error to CloudWatch
    logger.error('Static chatbot error', {
      requestId,
      error: errorContext.error,
      stack: errorContext.stack,
      ...(errorContext.promptData && { promptDataLength: JSON.stringify(errorContext.promptData).length }),
      ...(errorContext.question && { questionPreview: errorContext.question.substring(0, 100) + (errorContext.question.length > 100 ? '...' : '') })
    });

    // Return appropriate static response based on chat type
    const fallbackResponse = DEFAULT_STATIC_RESPONSES[chatType] || DEFAULT_STATIC_RESPONSES.default;
    logger.info('Returning fallback response after error', {
      responseLength: fallbackResponse?.length || 0,
    });

    return fallbackResponse;
  }
}

/**
 * Gets the appropriate chatbot handler function based on chat type
 * @param {string} chatType - The type of chat (from ChatTypeEnum)
 * @returns {Function|null} The appropriate chatbot handler function or null if not found
 */
function getChatbotFunction(chatType) {
  // Map chat types to their corresponding dynamic chatbot handlers
  const handlerMap = {
    [ChatTypeEnum.COMMUNITY]: dynamicChatbot.communityEventsChatbotDynamic,
    [ChatTypeEnum.FAMILY]: dynamicChatbot.familyEventsChatbotDynamic,
    [ChatTypeEnum.KNOWLEDGE]: dynamicChatbot.knowledgeChatbotDynamic,
    [ChatTypeEnum.UNIFY]: dynamicChatbot.unifyChatbotDynamic
  };

  const handler = handlerMap[chatType];
  if (!handler) {
    console.warn(`No handler found for chat type: ${chatType}`);
  }
  return handler || null;
}

// Wrapper function to maintain backward compatibility
async function storeChatbotPromptInDynamoDB(userId, chatType, apolloClient) {
  const logger = createLogger('storeChatbotPromptInDynamoDB');
  
  logger.info('Storing chatbot prompt in DynamoDB', { 
    userId, 
    chatType,
    hasApolloClient: !!apolloClient
  });

  try {
    return await _storeChatbotPromptInDynamoDB(
      userId,
      chatType,
      apolloClient,
      getChatbotFunction,
      getChatbotPromptDataFromGraphQLApi
    );
  } catch (error) {
    logger.error('Error in storeChatbotPromptInDynamoDB', {
      error: error.message,
      stack: error.stack,
      userId,
      chatType
    });
    throw error;
  }
}

module.exports = {
  getChatbotAnswer,
  removeChatbotPromptFromDynamoDB,
  storeChatbotPromptInDynamoDB,
  getChatbotPrompt,
  getChatbotPromptDataFromDynamoDB,
  getChatbotPromptDataFromGraphQLApi,
};
