const { v4: uuidv4 } = require('uuid');
const { createLogger, logError } = require('../shared/utils/logger');
const { getItem } = require('../shared/database/dynamoUtils');
const { 
  DEFAULT_STATIC_RESPONSES, 
  ChatTypeEnum,
  TABLES 
} = require('../shared/utils/constants');
const { getChatContext } = require('./chatHistoryService');

// Import prompt store functions
const {
  getChatbotPrompt,
  getChatbotPromptDataFromDynamoDB,
  _storeChatbotPromptInDynamoDB,
  removeChatbotPromptFromDynamoDB
} = require('./promptStore');

// Import GraphQL queries
const {
  GET_USER_FOR_COMMUNITY,
  GET_USER_FOR_FAMILY
} = require('../graphql/queries');

// Import utility functions
const {
  fetchBasicUserData,
  fetchKnowledgeData,
  fetchUnifiedData,
  extractOrganizations
} = require('../shared/utils/chatbotUtils');

// Import static prompt handlers
const {
  getCommunityEventsPrompt,
  getFamilyPrompt,
  getKnowledgePrompt,
  getUnifyPrompt,
} = require('../handlers/static');

// Import dynamic chatbot handlers
const dynamicChatbot = require('./dynamicChatbot');

// Import dynamic chat handlers
const {
  communityChatbot,
  familyChatbot,
  knowledgeChatbot,
  unifyChatbot
} = require('../handlers/dynamic');

const logger = createLogger('chatbot');

// ========== Utility Functions ==========

/**
 * Creates a logger with request context and step logging utilities
 * @param {string} operation - The operation name
 * @param {string} requestId - The request ID
 * @param {string} chatType - The chat type
 * @param {string} userId - The user ID
 * @returns {Object} Logger with context and utility functions
 */
function createLoggerWithContext(operation, requestId, chatType, userId) {
  let logSequence = 1;

  const loggerWithContext = logger.child({
    operation,
    requestId,
    chatType,
    userId,
    timestamp: new Date().toISOString(),
  });

  const logStep = (message, data = {}) => {
    loggerWithContext.info({ sequence: logSequence++, ...data }, message);
  };

  const logErrorStep = (message, error, data = {}) => {
    loggerWithContext.error({
      sequence: `✗${logSequence++}`,
      error: error?.message || 'Unknown error',
      stack: error?.stack,
      ...data
    }, message);
  };

  return { loggerWithContext, logStep, logErrorStep, getSequence: () => logSequence };
}

/**
 * Validates chatbot input parameters
 * @param {string} question - The user's question
 * @param {string} userId - The ID of the user
 * @param {string} chatType - The type of chat (Community, Family, Knowledge, Unify)
 * @param {Object} loggerWithContext - Logger instance with request context
 * @throws {Error} If any validation fails
 */
function validateChatbotInputs(question, userId, chatType, loggerWithContext) {
  loggerWithContext.debug('Validating chatbot inputs');

  const validationErrors = [];

  if (!userId) {
    validationErrors.push('Missing required parameter: userId is required');
  }

  if (!chatType) {
    validationErrors.push('Missing required parameter: chatType is required');
  }

  if (!question || typeof question !== 'string' || !question.trim()) {
    validationErrors.push('Invalid or empty question provided');
  }

  if (validationErrors.length > 0) {
    const error = new Error(validationErrors.join('; '));
    loggerWithContext.error('Validation error', { error: error.message, validationErrors });
    throw error;
  }

  loggerWithContext.debug('Input validation successful');
}

/**
 * Gets the appropriate static handler based on chat type
 * @param {string} chatType - The chat type
 * @returns {Function|null} The handler function or null
 */
function getStaticHandler(chatType) {
  const handlerMap = {
    [ChatTypeEnum.COMMUNITY]: getCommunityEventsPrompt,
    [ChatTypeEnum.FAMILY]: getFamilyPrompt,
    [ChatTypeEnum.KNOWLEDGE]: getKnowledgePrompt,
    [ChatTypeEnum.UNIFY]: getUnifyPrompt,
  };

  return handlerMap[chatType] || null;
}

/**
 * Creates prompt data with user data and chat history
 * @param {Object} userData - The user data
 * @param {string} chatHistory - The chat history
 * @param {string} chatType - The chat type
 * @param {string} userId - The user ID
 * @returns {Object} The formatted prompt data
 */
function createPromptData(userData, chatHistory, chatType, userId) {
  const baseMetadata = {
    timestamp: new Date().toISOString(),
    chatType,
    userId
  };

  if (userData && typeof userData === 'object' && !Array.isArray(userData)) {
    return {
      ...userData,
      chatHistory: chatHistory || '',
      _metadata: baseMetadata
    };
  }

  return {
    chatHistory: chatHistory || '',
    _metadata: {
      ...baseMetadata,
      note: userData === null ? 'Error parsing userData' : 'No valid userData provided'
    }
  };
}

/**
 * Gets the appropriate dynamic handler based on chat type
 * @param {string} chatType - The chat type
 * @returns {Function|null} The handler function or null
 */
function getDynamicHandler(chatType) {
  const handlerMap = {
    [ChatTypeEnum.COMMUNITY]: communityChatbot,
    [ChatTypeEnum.FAMILY]: familyChatbot,
    [ChatTypeEnum.KNOWLEDGE]: knowledgeChatbot,
    [ChatTypeEnum.UNIFY]: unifyChatbot,
  };

  return handlerMap[chatType] || null;
}

/**
 * Creates error context for logging
 * @param {Error} error - The error object
 * @param {string} chatType - The chat type
 * @param {Object} apolloClient - The Apollo client
 * @param {number} startTime - The start time
 * @returns {Object} Error context object
 */
function createErrorContext(error, chatType, apolloClient, startTime) {
  return {
    chatType,
    hasApolloClient: !!apolloClient,
    errorMessage: error.message,
    errorName: error.name,
    errorStack: error.stack,
    executionTimeMs: Date.now() - startTime,
  };
}

/**
 * Handles fallback response generation
 * @param {string} question - The user's question
 * @param {string} userId - The user ID
 * @param {string} chatType - The chat type
 * @param {Object} apolloClient - The Apollo client
 * @param {string} reason - The reason for fallback
 * @param {Object} logStep - The logging function
 * @returns {Promise<string>} The fallback response
 */
async function handleFallbackResponse(question, userId, chatType, apolloClient, reason, logStep) {
  logStep(`✗ Falling back to static response due to ${reason}`);
  return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
}

async function getChatbotAnswer(question, userId, chatType, apolloClient) {
  const operation = 'getChatbotAnswer';
  const requestId = `chat-${uuidv4()}`;
  const startTime = Date.now();

  // Create logger with context and utility functions
  const { loggerWithContext, logStep, logErrorStep, getSequence } = createLoggerWithContext(
    operation, requestId, chatType, userId
  );

  // Log execution start

  // Track if we should use static fallback
  let useStaticFallback = false;
  let staticFallbackResponse = null;
  
  try {
    // Main try block for the entire function
    // Warn if Apollo Client is missing
    if (!apolloClient) {
      logStep('2. ⚠️ Apollo Client is not available. Chat history will not be included.');
    } else {
      logStep('2. ✅ Apollo Client is available');
    }

    // Validate inputs
    logStep('3. 🔍 Validating input parameters');
    validateChatbotInputs(question, userId, chatType, loggerWithContext);
    logStep('4. ✅ Input validation successful');

    // Fetch prompt configuration from DynamoDB
    let promptConfig;
    try {
      logStep('5. 📝 Fetching prompt configuration from DynamoDB', { 
        chatType,
        tableName: 'chatbotPrompt'
      });
      
      // Try to get the dynamic prompt
      promptConfig = await getChatbotPrompt(chatType);
      
      // If no dynamic config, prepare static fallback but continue to try Bedrock
      if (!promptConfig || !promptConfig.template) {
        logStep('6. ⚠️ No dynamic prompt configuration found, will try Bedrock with default settings', {
          hasPromptConfig: !!promptConfig,
          hasTemplate: !!promptConfig?.template,
          table: TABLES.CHATBOT_PROMPT,
        });
        
        // Generate static response as fallback in case Bedrock fails
        logStep('7. ⏳ Preparing static response as fallback');
        staticFallbackResponse = await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
        useStaticFallback = true;
        
        logStep('8. ✅ Static fallback response prepared', {
          responseLength: staticFallbackResponse?.length || 0,
          responsePreview: staticFallbackResponse?.substring(0, 100) + (staticFallbackResponse?.length > 100 ? '...' : '')
        });
      } else {
        logStep('6. ✅ Successfully fetched prompt configuration', {
          configKeys: Object.keys(promptConfig),
          hasTemplate: !!promptConfig.template,
          hasInstructions: !!promptConfig.instructions,
        });
      }
    } catch (error) {
      logErrorStep('✗ Error fetching prompt configuration', error, {
        chatType,
        requestId,
      });

      const staticResponse = await handleFallbackResponse(
        question, userId, chatType, apolloClient, 'configuration error', logStep
      );

      const endTime = Date.now();
      logStep(`✗ Execution completed with error fallback in ${endTime - startTime}ms`, {
        responseLength: staticResponse?.length || 0,
        executionTimeMs: endTime - startTime,
        executionEnd: new Date().toISOString(),
        responseType: 'error_fallback',
      });

      return staticResponse;
    }

    // Get user-specific data for the prompt
    let promptData = {};
    if (apolloClient) {
      try {
        logStep('9. 🔍 Fetching user-specific prompt data from DynamoDB', { 
          tableName: process.env.CHATBOT_PROMPTS_DATA_TABLE || 'ChatbotPromptsData'
        });
        
        promptData = await getChatbotPromptDataFromDynamoDB(
          userId,
          chatType,
          apolloClient
        );
        
        if (!promptData || Object.keys(promptData).length === 0) {
          logStep('10. ⚠️ No user-specific prompt data found in DynamoDB', { 
            tableName: process.env.CHATBOT_PROMPTS_DATA_TABLE || 'ChatbotPromptsData'
          });
        } else {
          logStep('10. ✅ Successfully retrieved user-specific prompt data', { 
            dataKeys: Object.keys(promptData),
            hasOrganizations: 'organizations' in promptData,
            organizationCount: promptData.organizations?.length || 0,
            dataPreview: JSON.stringify(promptData).substring(0, 200) + 
              (JSON.stringify(promptData).length > 200 ? '...' : '')
          });
        }
      } catch (error) {
        logErrorStep('✗ Error fetching user prompt data', error, {
          tableName: process.env.CHATBOT_PROMPTS_DATA_TABLE || 'ChatbotPromptsData'
        });
        return await handleFallbackResponse(
          question, userId, chatType, apolloClient, 'error fetching user data', logStep
        );
      }
    }

    // If we get here but have no prompt data, use static fallback if available
    if (!promptData || Object.keys(promptData).length === 0) {
      logStep('11. ⚠️ No prompt data available, checking for static fallback');
      if (useStaticFallback && staticFallbackResponse) {
        logStep('12. 🔄 Using pre-generated static fallback response');
        return staticFallbackResponse;
      }
      // If we don't have a static fallback, generate one now
      logStep('12. ⏳ Generating new static fallback response');
      return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
    }

    const isDynamic = promptData.isDynamic || false;
    const promptDataString = JSON.stringify(promptData);
    logStep(`11. ✅ Found ${isDynamic ? 'dynamic' : 'static'} prompt data`, {
      promptLength: promptDataString.length,
      promptStart: promptDataString.substring(0, 100) + '...',
      isDynamic: isDynamic,
      dataKeys: Object.keys(promptData)
    });

    logStep('12. 🔄 Getting dynamic handler for chat type', { chatType });

    const handler = getDynamicHandler(chatType);
    if (!handler) {
      logStep('13. ⚠️ No dynamic handler found, falling back to static prompt', {
        chatType
      });
      return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
    }

    logStep(`13. 🚀 Invoking dynamic handler: ${handler.name}`, {
      questionLength: question.length,
      promptDataLength: JSON.stringify(promptData).length,
      promptDataKeys: Object.keys(promptData)
    });

    try {
      const result = await handler(question, promptData, userId, apolloClient);
      logStep('14. ✅ Dynamic handler completed successfully', {
        resultLength: result?.length || 0,
        resultPreview: result?.substring(0, 100) + (result?.length > 100 ? '...' : '')
      });
      
      if (typeof result === 'string' && result.trim()) {
        return result;
      }
      throw new Error('Handler returned empty or invalid response');
    } catch (error) {
      logErrorStep('✗ Error in dynamic handler', error, {
        handlerName: handler?.name || 'unknown',
      });
      
      // Use pre-generated static fallback if available
      if (useStaticFallback && staticFallbackResponse) {
        logStep('✗ Using pre-generated static fallback after dynamic handler error');
        return staticFallbackResponse;
      }
      
      // Otherwise generate a new static response
      logStep('✗ Generating new static fallback after dynamic handler error');
      return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
    }
  } catch (unhandledError) {
    // Top‐level catch for any other errors
    logErrorStep('✗ Unexpected error in getChatbotAnswer', unhandledError);
    
    // Use pre-generated static fallback if available
    if (useStaticFallback && staticFallbackResponse) {
      logStep('✗ Using pre-generated static fallback after unhandled error');
      return staticFallbackResponse;
    }
    
    // Fall back to generating a new static answer on any unhandled exception
    logStep('✗ Generating new static fallback after unhandled error');
    return await getStaticChatbotAnswer(question, userId, chatType, apolloClient);
  } finally {
    const endTime = Date.now();
    logStep(`🏁 Execution completed in ${endTime - startTime}ms`, {
      executionTimeMs: endTime - startTime,
      executionEnd: new Date().toISOString(),
      totalSteps: getSequence() - 1
    });
  }
}

// Chatbot handler functions have been moved to separate files in the handlers/dynamic directory

async function getChatbotPromptDataFromGraphQLApi(userId, apolloClient, type) {
  logger.debug('getChatbotPromptDataFromGraphQLApi called', { type });

  try {
    switch (type) {
      case ChatTypeEnum.COMMUNITY:
        return await fetchBasicUserData(apolloClient, GET_USER_FOR_COMMUNITY, userId);

      case ChatTypeEnum.FAMILY:
        return await fetchBasicUserData(apolloClient, GET_USER_FOR_FAMILY, userId);

      case ChatTypeEnum.KNOWLEDGE:
        return await fetchKnowledgeData(apolloClient, userId);

      case ChatTypeEnum.UNIFY: {
        // Return the object directly, not a string
        const result = await fetchUnifiedData(apolloClient, userId);
        return result;
      }

      default:
        logger.warn('Unknown chat type', { type });
        return null;
    }
  } catch (error) {
    logError(
      createLogger({}, { functionName: 'getChatbotPromptDataFromGraphQLApi' }),
      error,
      'Error in getChatbotPromptDataFromGraphQLApi'
    );
    return null;
  }
}

// ========== Helper Functions ==========


/**
 * Fallback function when no prompt configuration is found in DynamoDB
 * @param {string} question - The user's question
 * @param {string} userId - The ID of the user
 * @param {string} chatType - The type of chat (Community, Family, Knowledge, Unify)
 * @param {Object} apolloClient - The Apollo Client instance (kept for backward compatibility)
 * @returns {Promise<string>} - The chatbot's response
 */
async function getStaticChatbotAnswer(question, userId, chatType, apolloClient) {
  const requestId = `static-${uuidv4()}`;
  const operation = 'getStaticChatbotAnswer';
  const startTime = Date.now();
  
  // Create a child logger with request context
  const logger = createLogger(operation).child({
    requestId,
    userId,
    chatType,
    questionLength: question?.length || 0
  });
  
  logger.info('Starting static chatbot answer generation', {
    chatType,
    questionPreview: question ? question.substring(0, 50) + (question.length > 50 ? '...' : '') : 'No question',
    hasApolloClient: !!apolloClient
  });
  
  try {
    const userData = await getChatbotPromptDataFromGraphQLApi(userId, apolloClient, chatType);

    logger.debug('Retrieved user data from GraphQL API', {
      hasUserData: !!userData,
      dataType: userData ? typeof userData : 'none',
      dataPreview: userData ? 'Data available' : 'No data'
    });
    
    // Get the appropriate handler based on chat type
    const handler = getStaticHandler(chatType);
    if (!handler) {
      const warnMsg = `No handler found for chat type: ${chatType}`;
      logger.warn(warnMsg);
    }
    
    logger.debug('Selected static prompt handler', {
      chatType,
      handler: handler ? handler.name : 'none'
    });
    
    let response = DEFAULT_STATIC_RESPONSES[chatType] || DEFAULT_STATIC_RESPONSES.default;
    let chatHistory = ''; // Initialize chatHistory at function scope

    try {
      // Get chat history for context
      logger.info('🔍 [CHAT_HISTORY_DEBUG] Fetching chat history', {
        userId,
        chatType,
        requestId,
        operation: 'getStaticChatbotAnswer'
      });

      chatHistory = await getChatContext(userId, { limit: 3, chatType });

      logger.info('📋 [CHAT_HISTORY_DEBUG] Retrieved chat history for static response', {
        userId,
        chatType,
        requestId,
        hasHistory: !!chatHistory,
        historyLength: chatHistory?.length || 0,
        preview: chatHistory ? chatHistory.substring(0, 100) + (chatHistory.length > 100 ? '...' : '') : 'NO_HISTORY',
        fullHistory: chatHistory || 'EMPTY'
      });
      
      // Parse the userData if it's a string
      let parsedUserData;
      try {
        parsedUserData = typeof userData === 'string' ? JSON.parse(userData) : userData;
        logger.debug('Parsed userData', { type: typeof parsedUserData });
      } catch (parseError) {
        logger.error('Error parsing userData', { error: parseError.message });
        parsedUserData = null;
      }
      
      // Create promptData with parsed user data and chat history
      const promptData = createPromptData(parsedUserData, chatHistory, chatType, userId);

      logger.debug('Created promptData', {
        keys: Object.keys(promptData),
        hasUserData: !!userData,
        chatHistoryLength: chatHistory?.length || 0
      });
      
      // Try to get a more specific response using the appropriate handler
      let prompt;
      try {
        if (chatHistory) {
          // If we have chat history, prepend it to the question
          const questionWithHistory = `${chatHistory}Question: ${question}`;

          logger.info('🔗 [CHAT_HISTORY_DEBUG] Using question with chat history', {
            userId,
            chatType,
            requestId,
            originalQuestion: question,
            chatHistoryLength: chatHistory.length,
            questionWithHistoryLength: questionWithHistory.length,
            questionWithHistoryPreview: questionWithHistory.substring(0, 200) + (questionWithHistory.length > 200 ? '...' : ''),
            handlerName: handler?.name || 'unknown'
          });

          prompt = await handler(questionWithHistory, promptData, userId, apolloClient);
        } else {
          // Otherwise, just use the original question
          logger.info('❌ [CHAT_HISTORY_DEBUG] Using question WITHOUT chat history', {
            userId,
            chatType,
            requestId,
            originalQuestion: question,
            reason: 'No chat history available',
            handlerName: handler?.name || 'unknown'
          });

          prompt = await handler(question, promptData, userId, apolloClient);
        }

        logger.info('✅ [CHAT_HISTORY_DEBUG] Prompt generated successfully', {
          userId,
          chatType,
          requestId,
          promptLength: prompt?.length || 0,
          promptPreview: prompt ? prompt.substring(0, 100) + (prompt.length > 100 ? '...' : '') : 'NO_PROMPT'
        });
      } catch (handlerError) {
        logger.error('❌ [CHAT_HISTORY_DEBUG] Error in handler', {
          userId,
          chatType,
          requestId,
          error: handlerError.message,
          handlerName: handler?.name || 'unknown'
        });
        throw handlerError;
      }
      if (prompt && typeof prompt === 'string' && prompt.trim().length > 0) {
        response = prompt;
      } else if (userData) {
        // Extract organizations from the already parsed userData
        const orgs = extractOrganizations(userData);
        
        if (orgs.length > 0) {
          logger.debug('Extracted organizations', { 
            orgCount: orgs.length,
            orgNames: orgs.map(o => o.name).join(', ')
          });
          
          // If user asked about organizations and we have that data
          if (question.toLowerCase().includes('organization') || 
              question.toLowerCase().includes('org')) {
            response = `You are associated with ${orgs.length} organization(s): ` +
              orgs.map(o => o.name).join(', ');
          }
        }
      }
    } catch (error) {
      logger.error('Error processing user data for response', {
        error: error.message,
        stack: error.stack
      });
    }

    const executionTime = Date.now() - startTime;

    logger.info('Successfully generated static response', {
      chatType,
      responseLength: response?.length || 0,
      executionTimeMs: executionTime,
      hasChatHistory: !!chatHistory,
      chatHistoryLength: chatHistory?.length || 0
    });

    return response;
  } catch (error) {
    const errorContext = createErrorContext(error, chatType, apolloClient, startTime);
    logError(logger, error, operation, errorContext);

    // Log the full error to CloudWatch
    logger.error('Static chatbot error', {
      requestId,
      error: errorContext.error,
      stack: errorContext.stack,
      ...(errorContext.promptData && { promptDataLength: JSON.stringify(errorContext.promptData).length }),
      ...(errorContext.question && { questionPreview: errorContext.question.substring(0, 100) + (errorContext.question.length > 100 ? '...' : '') })
    });

    // Return appropriate static response based on chat type
    const fallbackResponse = DEFAULT_STATIC_RESPONSES[chatType] || DEFAULT_STATIC_RESPONSES.default;
    logger.info('Returning fallback response after error', {
      responseLength: fallbackResponse?.length || 0,
    });

    return fallbackResponse;
  }
}

/**
 * Gets the appropriate chatbot handler function based on chat type
 * @param {string} chatType - The type of chat (from ChatTypeEnum)
 * @returns {Function|null} The appropriate chatbot handler function or null if not found
 */
function getChatbotFunction(chatType) {
  // Map chat types to their corresponding dynamic chatbot handlers
  const handlerMap = {
    [ChatTypeEnum.COMMUNITY]: dynamicChatbot.communityEventsChatbotDynamic,
    [ChatTypeEnum.FAMILY]: dynamicChatbot.familyEventsChatbotDynamic,
    [ChatTypeEnum.KNOWLEDGE]: dynamicChatbot.knowledgeChatbotDynamic,
    [ChatTypeEnum.UNIFY]: dynamicChatbot.unifyChatbotDynamic
  };

  const handler = handlerMap[chatType];
  if (!handler) {
    logger.warn(`No handler found for chat type: ${chatType}`);
  }
  return handler || null;
}

// Wrapper function to maintain backward compatibility
async function storeChatbotPromptInDynamoDB(userId, chatType, apolloClient) {
  const logger = createLogger('storeChatbotPromptInDynamoDB');
  
  logger.info('Storing chatbot prompt in DynamoDB', { 
    userId, 
    chatType,
    hasApolloClient: !!apolloClient
  });

  try {
    return await _storeChatbotPromptInDynamoDB(
      userId,
      chatType,
      apolloClient,
      getChatbotFunction,
      getChatbotPromptDataFromGraphQLApi
    );
  } catch (error) {
    logger.error('Error in storeChatbotPromptInDynamoDB', {
      error: error.message,
      stack: error.stack,
      userId,
      chatType
    });
    throw error;
  }
}

module.exports = {
  getChatbotAnswer,
  removeChatbotPromptFromDynamoDB,
  storeChatbotPromptInDynamoDB,
  getChatbotPrompt,
  getChatbotPromptDataFromDynamoDB,
  getChatbotPromptDataFromGraphQLApi,
};
