const { createLogger } = require('../../shared/utils/logger');
const { getCommunityEventsPrompt } = require('../static/communityHandler');
const { getFamilyPrompt } = require('../static/familyHandler');
const { getKnowledgePrompt } = require('../static/knowledgeHandler');
const { getUnifyPrompt } = require('../static/unifyHandler');
const { formatCommunityData } = require('../../shared/utils/formatters/communityDataFormatter');

/**
 * Generic chatbot handler that can be used for all dynamic handlers
 * @param {string} handlerType - The type of handler (community, family, knowledge, unify)
 * @param {string} question - The user's question
 * @param {Object} promptData - The data to use for generating the prompt
 * @param {string} userId - The ID of the user
 * @param {Object} apolloClient - The Apollo Client instance
 * @returns {Promise<string>} - The chatbot's response
 */
async function genericChatbotHandler(handlerType, question, promptData, userId, apolloClient) {
  const logger = createLogger(`${handlerType}Handler`);
  const startTime = Date.now();
  const requestId = `${handlerType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  try {
    logger.info(`=== [${handlerType}Chatbot] START - ${requestId} ===`, {
      question: question?.substring(0, 200),
      hasPromptData: !!promptData,
      userId,
      hasApolloClient: !!apolloClient
    });

    let result;
    let processedData = promptData;

    // Handle special formatting for community data
    if (handlerType === 'community') {
      processedData = formatCommunityData(promptData);
    }

    // Call the appropriate static handler based on type
    switch (handlerType) {
      case 'community':
        result = await getCommunityEventsPrompt(question, processedData, userId, apolloClient);
        break;
      case 'family':
        result = await getFamilyPrompt(question, processedData, userId, apolloClient);
        break;
      case 'knowledge':
        result = await getKnowledgePrompt(question, processedData, userId, apolloClient);
        break;
      case 'unify':
        result = await getUnifyPrompt(question, processedData, userId, apolloClient);
        break;
      default:
        throw new Error(`Unknown handler type: ${handlerType}`);
    }

    const endTime = Date.now();

    logger.info(`[${handlerType}Chatbot] Request completed successfully in ${endTime - startTime}ms`, {
      resultLength: result?.length || 0,
      requestId
    });
    logger.info(`=== [${handlerType}Chatbot] END - ${requestId} (${endTime - startTime}ms) ===`);
    return result;
  } catch (error) {
    const errorTime = Date.now();
    logger.error(`[${handlerType}Chatbot] Request failed after ${errorTime - startTime}ms:`, {
      error: error.message,
      stack: error.stack,
      userId,
      requestId,
      hasApolloClient: !!apolloClient
    });
    logger.info(`=== [${handlerType}Chatbot] ERROR - ${requestId} (${errorTime - startTime}ms) ===`);
    throw error; // Re-throw to be handled by the caller
  }
}

/**
 * Handles community-related chat functionality
 * @param {string} question - The user's question
 * @param {Object} promptData - The data to use for generating the prompt
 * @param {string} userId - The ID of the user
 * @param {Object} apolloClient - The Apollo Client instance
 * @returns {Promise<string>} - The chatbot's response
 */
async function communityChatbot(question, promptData, userId, apolloClient) {
  return genericChatbotHandler('community', question, promptData, userId, apolloClient);
}

/**
 * Handles family-related chat functionality
 * @param {string} question - The user's question
 * @param {Object} promptData - The data to use for generating the prompt
 * @param {string} userId - The ID of the user
 * @param {Object} apolloClient - The Apollo Client instance
 * @returns {Promise<string>} - The chatbot's response
 */
async function familyChatbot(question, promptData, userId, apolloClient) {
  return genericChatbotHandler('family', question, promptData, userId, apolloClient);
}

/**
 * Handles knowledge-related chat functionality
 * @param {string} question - The user's question
 * @param {Object} promptData - The data to use for generating the prompt
 * @param {string} userId - The ID of the user
 * @param {Object} apolloClient - The Apollo Client instance
 * @returns {Promise<string>} - The chatbot's response
 */
async function knowledgeChatbot(question, promptData, userId, apolloClient) {
  return genericChatbotHandler('knowledge', question, promptData, userId, apolloClient);
}

/**
 * Handles unified chat functionality
 * @param {string} question - The user's question
 * @param {Object} promptData - The data to use for generating the prompt
 * @param {string} userId - The ID of the user
 * @param {Object} apolloClient - The Apollo Client instance
 * @returns {Promise<string>} - The chatbot's response
 */
async function unifyChatbot(question, promptData, userId, apolloClient) {
  return genericChatbotHandler('unify', question, promptData, userId, apolloClient);
}

module.exports = {
  communityChatbot,
  familyChatbot,
  knowledgeChatbot,
  unifyChatbot
};
